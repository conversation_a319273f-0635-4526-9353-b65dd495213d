package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for EmojiCategoryService.
 * Tests Firebase Remote Config integration and fallback mechanisms.
 * 
 * Following established testing patterns:
 * - Uses MockK for mocking dependencies
 * - Tests both success and failure scenarios
 * - Verifies fallback behavior
 * - Uses runTest for coroutine testing
 */
class EmojiCategoryServiceTest {

    private lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    private lateinit var gson: Gson
    private lateinit var emojiCategoryService: EmojiCategoryService

    @Before
    fun setUp() {
        remoteConfigHelper = mockk()
        gson = Gson() // Use real Gson for JSON parsing
        emojiCategoryService = EmojiCategoryService(remoteConfigHelper, gson)
    }

    @Test
    fun `getEmojiCategories returns parsed categories when remote config has valid data`() = runTest {
        // Arrange
        val validJson = """
            [
                {"id":"hot_category","priority":1,"name":"🔥 HOT","status":true,"is_new":false},
                {"id":"character_category","priority":3,"name":"Character","status":true,"is_new":false},
                {"id":"heart_category","priority":4,"name":"Heart","status":true,"is_new":true}
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("emoji_categories") } returns validJson

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertEquals(3, result.size)
        
        val hotCategory = result.find { it.id == "hot_category" }
        assertNotNull(hotCategory)
        assertEquals("🔥 HOT", hotCategory?.name)
        assertEquals(1, hotCategory?.priority)
        assertTrue(hotCategory?.status == true)
        assertFalse(hotCategory?.isNew == true)
        
        val heartCategory = result.find { it.id == "heart_category" }
        assertNotNull(heartCategory)
        assertTrue(heartCategory?.isNew == true)
        
        verify { remoteConfigHelper.getString("emoji_categories") }
    }

    @Test
    fun `getEmojiCategories filters out disabled categories`() = runTest {
        // Arrange
        val jsonWithDisabledCategory = """
            [
                {"id":"hot_category","priority":1,"name":"🔥 HOT","status":true,"is_new":false},
                {"id":"disabled_category","priority":2,"name":"Disabled","status":false,"is_new":false},
                {"id":"character_category","priority":3,"name":"Character","status":true,"is_new":false}
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("emoji_categories") } returns jsonWithDisabledCategory

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertEquals(2, result.size)
        assertNull(result.find { it.id == "disabled_category" })
        assertNotNull(result.find { it.id == "hot_category" })
        assertNotNull(result.find { it.id == "character_category" })
    }

    @Test
    fun `getEmojiCategories sorts categories by priority`() = runTest {
        // Arrange
        val jsonWithUnsortedCategories = """
            [
                {"id":"character_category","priority":3,"name":"Character","status":true,"is_new":false},
                {"id":"hot_category","priority":1,"name":"🔥 HOT","status":true,"is_new":false},
                {"id":"heart_category","priority":2,"name":"Heart","status":true,"is_new":false}
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("emoji_categories") } returns jsonWithUnsortedCategories

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertEquals(3, result.size)
        assertEquals("hot_category", result[0].id) // priority 1
        assertEquals("heart_category", result[1].id) // priority 2
        assertEquals("character_category", result[2].id) // priority 3
    }

    @Test
    fun `getEmojiCategories returns minimal fallback categories when remote config is empty`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("emoji_categories") } returns ""

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertTrue(result.isNotEmpty())
        assertEquals(3, result.size) // Expected minimal fallback categories count

        val hotCategory = result.find { it.id == "hot_category" }
        assertNotNull(hotCategory)
        assertEquals("🔥 HOT", hotCategory?.name)

        val characterCategory = result.find { it.id == "character_category" }
        assertNotNull(characterCategory)
        assertEquals("Character", characterCategory?.name)

        val animalCategory = result.find { it.id == "animal_category" }
        assertNotNull(animalCategory)
        assertEquals("Animal", animalCategory?.name)
    }

    @Test
    fun `getEmojiCategories returns minimal fallback categories when remote config throws exception`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("emoji_categories") } throws RuntimeException("Network error")

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertTrue(result.isNotEmpty())
        assertEquals(3, result.size) // Expected minimal fallback categories count

        // Verify the minimal fallback categories are present
        val categoryIds = result.map { it.id }
        assertTrue(categoryIds.contains("hot_category"))
        assertTrue(categoryIds.contains("character_category"))
        assertTrue(categoryIds.contains("animal_category"))
    }

    @Test
    fun `getEmojiCategories returns fallback categories when JSON is invalid`() = runTest {
        // Arrange
        val invalidJson = "{ invalid json }"
        every { remoteConfigHelper.getString("emoji_categories") } returns invalidJson

        // Act
        val result = emojiCategoryService.getEmojiCategories()

        // Assert
        assertTrue(result.isNotEmpty())
        assertEquals(6, result.size) // Expected fallback categories count
    }

    @Test
    fun `isEmojiCategoryDataAvailable returns true when data exists`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("emoji_categories") } returns "some data"

        // Act
        val result = emojiCategoryService.isEmojiCategoryDataAvailable()

        // Assert
        assertTrue(result)
    }

    @Test
    fun `isEmojiCategoryDataAvailable returns false when data is empty`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("emoji_categories") } returns ""

        // Act
        val result = emojiCategoryService.isEmojiCategoryDataAvailable()

        // Assert
        assertFalse(result)
    }

    @Test
    fun `emoji category validation works correctly`() {
        // Test EmojiCategory validation
        val validCategory = EmojiCategory("test_id", 1, "Test Name", true, false)
        assertTrue(validCategory.isValid())
        
        val invalidCategory = EmojiCategory("", -1, "", true, false)
        assertFalse(invalidCategory.isValid())
    }

    @Test
    fun `emoji category mapping to BatteryStyleCategory works correctly`() {
        // Test mapping functionality
        val hotCategory = EmojiCategory("hot_category", 1, "🔥 HOT", true, false)
        assertEquals(com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.HOT, hotCategory.toBatteryStyleCategory())
        
        val unknownCategory = EmojiCategory("unknown_category", 1, "Unknown", true, false)
        assertNull(unknownCategory.toBatteryStyleCategory())
    }
}
